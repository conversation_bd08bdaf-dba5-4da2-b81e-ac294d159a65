"""
Word文档分章节管理MCP服务器
支持Word文档的章节划分、内容提取和上下文管理
"""

import os
import json
import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from mcp.server.fastmcp import FastMCP
from pydantic import BaseModel, Field
import tiktoken

# 初始化MCP服务器
mcp = FastMCP("WordSectionManager")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WordSection(BaseModel):
    """Word文档章节模型"""
    id: str = Field(description="章节唯一标识")
    title: str = Field(description="章节标题")
    content: str = Field(description="章节内容")
    level: int = Field(description="章节级别(1-6)")
    word_count: int = Field(description="字数统计")
    token_count: int = Field(description="Token数量")
    start_position: int = Field(description="在原文档中的起始位置")
    end_position: int = Field(description="在原文档中的结束位置")

class WordDocument(BaseModel):
    """Word文档模型"""
    id: str = Field(description="文档唯一标识")
    name: str = Field(description="文档名称")
    path: str = Field(description="文档路径")
    sections: List[WordSection] = Field(description="章节列表")
    total_words: int = Field(description="总字数")
    total_tokens: int = Field(description="总Token数")
    max_context_tokens: int = Field(default=120000, description="最大上下文Token限制")

class SectionSplitConfig(BaseModel):
    """章节分割配置"""
    max_tokens_per_section: int = Field(default=30000, description="每个章节最大Token数")
    min_tokens_per_section: int = Field(default=1000, description="每个章节最小Token数")
    overlap_tokens: int = Field(default=500, description="章节间重叠Token数")
    split_by_headings: bool = Field(default=True, description="是否按标题分割")
    heading_levels: List[int] = Field(default=[1, 2, 3], description="要识别的标题级别")

# 全局变量存储文档数据
documents_cache: Dict[str, WordDocument] = {}

def count_tokens(text: str, model: str = "gpt-4") -> int:
    """计算文本的Token数量"""
    try:
        encoding = tiktoken.encoding_for_model(model)
        return len(encoding.encode(text))
    except Exception as e:
        logger.warning(f"Token计算失败，使用估算方法: {e}")
        # 粗略估算：中文约1.5字符/token，英文约4字符/token
        chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
        other_chars = len(text) - chinese_chars
        return int(chinese_chars / 1.5 + other_chars / 4)

def extract_headings_from_text(text: str) -> List[Tuple[str, int, int]]:
    """从文本中提取标题
    返回: [(标题文本, 级别, 位置), ...]
    """
    headings = []
    lines = text.split('\n')
    position = 0
    
    for line in lines:
        line = line.strip()
        if line:
            # 检测标题模式
            level = 0
            title = line
            
            # 检测数字编号标题 (如: 1. 第一章, 1.1 小节)
            if line.startswith(('第', '章', '节', '部分')):
                level = 1
            elif any(line.startswith(f"{i}.") for i in range(1, 10)):
                if '.' in line and line.count('.') == 1:
                    level = 2
                elif '.' in line and line.count('.') == 2:
                    level = 3
            # 检测Markdown风格标题
            elif line.startswith('#'):
                level = min(line.count('#'), 6)
                title = line.lstrip('#').strip()
            
            if level > 0:
                headings.append((title, level, position))
        
        position += len(line) + 1  # +1 for newline
    
    return headings

def split_text_by_headings(text: str, config: SectionSplitConfig) -> List[WordSection]:
    """按标题分割文本为章节"""
    headings = extract_headings_from_text(text)
    sections = []
    
    if not headings:
        # 如果没有标题，按Token数量分割
        return split_text_by_tokens(text, config)
    
    for i, (title, level, start_pos) in enumerate(headings):
        # 确定章节结束位置
        if i + 1 < len(headings):
            end_pos = headings[i + 1][2]
        else:
            end_pos = len(text)
        
        # 提取章节内容
        content = text[start_pos:end_pos].strip()
        
        # 检查Token数量
        token_count = count_tokens(content)
        
        if token_count > config.max_tokens_per_section:
            # 如果章节过长，进一步分割
            sub_sections = split_large_section(content, title, level, config)
            sections.extend(sub_sections)
        else:
            section = WordSection(
                id=f"section_{len(sections) + 1}",
                title=title,
                content=content,
                level=level,
                word_count=len(content),
                token_count=token_count,
                start_position=start_pos,
                end_position=end_pos
            )
            sections.append(section)
    
    return sections

def split_large_section(content: str, title: str, level: int, config: SectionSplitConfig) -> List[WordSection]:
    """分割过长的章节"""
    sections = []
    max_tokens = config.max_tokens_per_section
    overlap = config.overlap_tokens
    
    # 按段落分割
    paragraphs = content.split('\n\n')
    current_content = ""
    current_tokens = 0
    section_count = 1
    
    for paragraph in paragraphs:
        paragraph_tokens = count_tokens(paragraph)
        
        if current_tokens + paragraph_tokens > max_tokens and current_content:
            # 创建当前章节
            section = WordSection(
                id=f"section_{len(sections) + 1}_{section_count}",
                title=f"{title} (第{section_count}部分)",
                content=current_content.strip(),
                level=level + 1,
                word_count=len(current_content),
                token_count=current_tokens,
                start_position=0,  # 相对位置
                end_position=len(current_content)
            )
            sections.append(section)
            
            # 准备下一个章节，包含重叠内容
            if overlap > 0:
                overlap_text = current_content[-overlap:]
                current_content = overlap_text + "\n\n" + paragraph
                current_tokens = count_tokens(current_content)
            else:
                current_content = paragraph
                current_tokens = paragraph_tokens
            
            section_count += 1
        else:
            current_content += "\n\n" + paragraph if current_content else paragraph
            current_tokens += paragraph_tokens
    
    # 添加最后一个章节
    if current_content:
        section = WordSection(
            id=f"section_{len(sections) + 1}_{section_count}",
            title=f"{title} (第{section_count}部分)" if section_count > 1 else title,
            content=current_content.strip(),
            level=level + (1 if section_count > 1 else 0),
            word_count=len(current_content),
            token_count=current_tokens,
            start_position=0,
            end_position=len(current_content)
        )
        sections.append(section)
    
    return sections

def split_text_by_tokens(text: str, config: SectionSplitConfig) -> List[WordSection]:
    """按Token数量分割文本"""
    sections = []
    max_tokens = config.max_tokens_per_section
    overlap = config.overlap_tokens
    
    # 按段落分割
    paragraphs = text.split('\n\n')
    current_content = ""
    current_tokens = 0
    section_count = 1
    
    for paragraph in paragraphs:
        paragraph_tokens = count_tokens(paragraph)
        
        if current_tokens + paragraph_tokens > max_tokens and current_content:
            # 创建章节
            section = WordSection(
                id=f"auto_section_{section_count}",
                title=f"第{section_count}部分",
                content=current_content.strip(),
                level=1,
                word_count=len(current_content),
                token_count=current_tokens,
                start_position=0,
                end_position=len(current_content)
            )
            sections.append(section)
            
            # 准备下一个章节
            if overlap > 0:
                overlap_text = current_content[-overlap:]
                current_content = overlap_text + "\n\n" + paragraph
                current_tokens = count_tokens(current_content)
            else:
                current_content = paragraph
                current_tokens = paragraph_tokens
            
            section_count += 1
        else:
            current_content += "\n\n" + paragraph if current_content else paragraph
            current_tokens += paragraph_tokens
    
    # 添加最后一个章节
    if current_content:
        section = WordSection(
            id=f"auto_section_{section_count}",
            title=f"第{section_count}部分",
            content=current_content.strip(),
            level=1,
            word_count=len(current_content),
            token_count=current_tokens,
            start_position=0,
            end_position=len(current_content)
        )
        sections.append(section)
    
    return sections

@mcp.tool()
async def load_word_document(file_path: str, config: Optional[Dict[str, Any]] = None) -> str:
    """
    加载Word文档并进行章节分割
    
    Args:
        file_path: Word文档路径
        config: 分割配置参数
    
    Returns:
        JSON格式的文档信息
    """
    try:
        # 解析配置
        split_config = SectionSplitConfig(**(config or {}))
        
        # 这里应该调用word-document-server来读取Word文档内容
        # 暂时使用模拟数据
        mock_content = """
第一章 引言

这是第一章的内容，介绍了项目的背景和目标。本章将详细阐述研究的意义和价值。

1.1 研究背景

在当前的技术发展背景下，人工智能技术正在快速发展。本研究旨在探索AI在文档处理领域的应用。

1.2 研究目标

本研究的主要目标包括：
1. 开发智能文档处理系统
2. 提高文档处理效率
3. 改善用户体验

第二章 方法论

本章介绍了研究采用的方法和技术路线。

2.1 技术架构

系统采用微服务架构，包含以下组件：
- 前端界面
- 后端服务
- 数据存储

2.2 实现方案

详细的实现方案将在后续章节中介绍。
        """
        
        # 分割文档
        sections = split_text_by_headings(mock_content, split_config)
        
        # 创建文档对象
        document = WordDocument(
            id=f"doc_{hash(file_path)}",
            name=os.path.basename(file_path),
            path=file_path,
            sections=sections,
            total_words=len(mock_content),
            total_tokens=count_tokens(mock_content)
        )
        
        # 缓存文档
        documents_cache[document.id] = document
        
        return json.dumps({
            "success": True,
            "document_id": document.id,
            "document": document.dict(),
            "message": f"成功加载文档，共分割为{len(sections)}个章节"
        }, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"加载文档失败: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "message": "文档加载失败"
        }, ensure_ascii=False, indent=2)

@mcp.tool()
async def get_document_sections(document_id: str) -> str:
    """
    获取文档的所有章节信息
    
    Args:
        document_id: 文档ID
    
    Returns:
        JSON格式的章节列表
    """
    try:
        if document_id not in documents_cache:
            return json.dumps({
                "success": False,
                "error": "Document not found",
                "message": "文档未找到，请先加载文档"
            }, ensure_ascii=False, indent=2)
        
        document = documents_cache[document_id]
        
        return json.dumps({
            "success": True,
            "document_id": document_id,
            "sections": [section.dict() for section in document.sections],
            "total_sections": len(document.sections),
            "total_tokens": document.total_tokens
        }, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"获取章节失败: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "message": "获取章节信息失败"
        }, ensure_ascii=False, indent=2)

@mcp.tool()
async def get_section_content(document_id: str, section_id: str) -> str:
    """
    获取指定章节的详细内容
    
    Args:
        document_id: 文档ID
        section_id: 章节ID
    
    Returns:
        JSON格式的章节内容
    """
    try:
        if document_id not in documents_cache:
            return json.dumps({
                "success": False,
                "error": "Document not found",
                "message": "文档未找到"
            }, ensure_ascii=False, indent=2)
        
        document = documents_cache[document_id]
        section = next((s for s in document.sections if s.id == section_id), None)
        
        if not section:
            return json.dumps({
                "success": False,
                "error": "Section not found",
                "message": "章节未找到"
            }, ensure_ascii=False, indent=2)
        
        return json.dumps({
            "success": True,
            "section": section.dict(),
            "context_info": {
                "fits_in_context": section.token_count <= document.max_context_tokens,
                "token_usage_percent": (section.token_count / document.max_context_tokens) * 100
            }
        }, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"获取章节内容失败: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "message": "获取章节内容失败"
        }, ensure_ascii=False, indent=2)

if __name__ == "__main__":
    logger.info("Word章节管理MCP服务器启动中...")
    mcp.run(transport='stdio')
