#!/usr/bin/env python3
"""
测试修复后的Word编辑Agent功能
"""

import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path

# 项目路径
PROJECT_ROOT = Path(__file__).parent
BACKEND_DIR = PROJECT_ROOT / "backend"
FRONTEND_DIR = PROJECT_ROOT / "frontend"

# 进程列表
processes = []

def signal_handler(sig, frame):
    """处理Ctrl+C信号，优雅关闭所有进程"""
    print("\n🛑 正在关闭所有服务...")
    
    for process in processes:
        if process.poll() is None:  # 进程仍在运行
            print(f"关闭进程 PID: {process.pid}")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
    
    print("✅ 所有服务已关闭")
    sys.exit(0)

def start_test_backend():
    """启动测试后端服务"""
    print("🚀 启动测试后端API服务...")
    
    try:
        process = subprocess.Popen(
            [sys.executable, "test_api_server.py"],
            cwd=BACKEND_DIR,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        processes.append(process)
        
        # 在后台线程中输出日志
        def log_output():
            for line in process.stdout:
                print(f"[后端] {line.strip()}")
        
        threading.Thread(target=log_output, daemon=True).start()
        
        print("✅ 测试后端API服务启动成功 (http://localhost:8001)")
        return process
        
    except Exception as e:
        print(f"❌ 后端服务启动失败: {e}")
        return None

def start_frontend():
    """启动前端服务"""
    print("🌐 启动前端开发服务器...")
    
    try:
        process = subprocess.Popen(
            ["pnpm", "dev"],
            cwd=FRONTEND_DIR,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        processes.append(process)
        
        # 在后台线程中输出日志
        def log_output():
            for line in process.stdout:
                print(f"[前端] {line.strip()}")
        
        threading.Thread(target=log_output, daemon=True).start()
        
        print("✅ 前端开发服务器启动成功 (http://localhost:3000)")
        return process
        
    except Exception as e:
        print(f"❌ 前端服务启动失败: {e}")
        return None

def main():
    """主函数"""
    print("🧪 Word编辑Agent功能测试")
    print("=" * 50)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    print("\n🚀 启动测试服务...")
    
    # 启动测试后端服务
    backend_process = start_test_backend()
    if not backend_process:
        print("❌ 后端服务启动失败，退出")
        sys.exit(1)
    
    # 等待后端服务启动
    time.sleep(3)
    
    # 启动前端服务
    frontend_process = start_frontend()
    if not frontend_process:
        print("❌ 前端服务启动失败，退出")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 测试服务启动成功！")
    print("📱 前端地址: http://localhost:3000")
    print("🧪 测试页面: http://localhost:3000/test-word")
    print("🔧 后端API: http://localhost:8001")
    print("📖 API文档: http://localhost:8001/docs")
    print("=" * 50)
    print("\n📋 测试清单:")
    print("1. ✅ 点击'选择文件'按钮应该弹出文件选择器，显示.doc和.docx文件")
    print("2. ✅ 拖拽.docx文件到Word预览区域应该被接受（不会显示图片/PDF错误）")
    print("3. ✅ 上传文件后应该自动解析章节")
    print("4. ✅ 选择章节后点击'开始编辑'应该有响应")
    print("5. ✅ 文件类型验证应该只接受Word文档")
    print("=" * 50)
    print("按 Ctrl+C 停止所有服务")
    
    # 等待所有进程
    try:
        while True:
            # 检查进程是否还在运行
            running_processes = [p for p in processes if p.poll() is None]
            if not running_processes:
                print("⚠️  所有进程已退出")
                break
            time.sleep(1)
    except KeyboardInterrupt:
        pass
    
    signal_handler(None, None)

if __name__ == "__main__":
    main()
