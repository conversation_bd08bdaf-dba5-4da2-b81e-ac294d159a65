"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Play, 
  CheckCircle, 
  XCircle, 
  Clock,
  FileText,
  MessageSquare,
  Settings
} from "lucide-react";

interface TestResult {
  name: string;
  status: 'pending' | 'running' | 'success' | 'error';
  message?: string;
  duration?: number;
}

export function WordEditingIntegrationTest() {
  const [tests, setTests] = useState<TestResult[]>([
    { name: "前端组件加载", status: 'pending' },
    { name: "Word预览组件", status: 'pending' },
    { name: "文件上传功能", status: 'pending' },
    { name: "后端连接", status: 'pending' },
    { name: "Word文档加载", status: 'pending' },
    { name: "章节分割", status: 'pending' },
    { name: "章节选择", status: 'pending' },
    { name: "文档编辑", status: 'pending' },
    { name: "保存功能", status: 'pending' },
  ]);

  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState<number>(-1);

  const updateTestStatus = (index: number, status: TestResult['status'], message?: string, duration?: number) => {
    setTests(prev => prev.map((test, i) => 
      i === index ? { ...test, status, message, duration } : test
    ));
  };

  const runTest = async (index: number): Promise<boolean> => {
    const test = tests[index];
    setCurrentTest(index);
    updateTestStatus(index, 'running');
    
    const startTime = Date.now();
    
    try {
      // 模拟测试执行
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
      
      // 模拟测试结果
      const success = Math.random() > 0.2; // 80% 成功率
      
      if (success) {
        const duration = Date.now() - startTime;
        updateTestStatus(index, 'success', '测试通过', duration);
        return true;
      } else {
        updateTestStatus(index, 'error', '测试失败：模拟错误');
        return false;
      }
    } catch (error) {
      updateTestStatus(index, 'error', `测试失败：${error}`);
      return false;
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    
    // 重置所有测试状态
    setTests(prev => prev.map(test => ({ ...test, status: 'pending' as const })));
    
    let passedCount = 0;
    
    for (let i = 0; i < tests.length; i++) {
      const success = await runTest(i);
      if (success) passedCount++;
      
      // 如果是关键测试失败，可能需要停止后续测试
      if (!success && i < 4) { // 前4个是基础测试
        console.log(`关键测试失败，停止后续测试: ${tests[i].name}`);
        break;
      }
    }
    
    setCurrentTest(-1);
    setIsRunning(false);
    
    console.log(`测试完成：${passedCount}/${tests.length} 通过`);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-gray-400" />;
      case 'running':
        return <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  const getStatusBadge = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary">等待中</Badge>;
      case 'running':
        return <Badge variant="default">运行中</Badge>;
      case 'success':
        return <Badge variant="default" className="bg-green-500">通过</Badge>;
      case 'error':
        return <Badge variant="destructive">失败</Badge>;
    }
  };

  const passedTests = tests.filter(t => t.status === 'success').length;
  const failedTests = tests.filter(t => t.status === 'error').length;
  const totalTests = tests.length;

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Word编辑Agent集成测试
          </CardTitle>
          <p className="text-sm text-gray-600">
            测试前后端集成和Word文档编辑功能
          </p>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-4">
              <div className="text-sm">
                <span className="font-medium">进度：</span>
                {passedTests + failedTests}/{totalTests}
              </div>
              <div className="text-sm">
                <span className="font-medium text-green-600">通过：{passedTests}</span>
              </div>
              <div className="text-sm">
                <span className="font-medium text-red-600">失败：{failedTests}</span>
              </div>
            </div>
            <Button 
              onClick={runAllTests} 
              disabled={isRunning}
              className="flex items-center gap-2"
            >
              <Play className="h-4 w-4" />
              {isRunning ? '运行中...' : '开始测试'}
            </Button>
          </div>

          <div className="space-y-2">
            {tests.map((test, index) => (
              <div
                key={index}
                className={`flex items-center justify-between p-3 rounded-lg border transition-colors ${
                  currentTest === index ? 'bg-blue-50 border-blue-200' : 'bg-gray-50'
                }`}
              >
                <div className="flex items-center gap-3">
                  {getStatusIcon(test.status)}
                  <span className="font-medium">{test.name}</span>
                  {test.message && (
                    <span className="text-sm text-gray-600">- {test.message}</span>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  {test.duration && (
                    <span className="text-xs text-gray-500">
                      {test.duration}ms
                    </span>
                  )}
                  {getStatusBadge(test.status)}
                </div>
              </div>
            ))}
          </div>

          {/* 测试结果汇总 */}
          {passedTests + failedTests === totalTests && (
            <div className="mt-6 p-4 rounded-lg bg-gray-50">
              <h3 className="font-medium mb-2">测试结果汇总</h3>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{passedTests}</div>
                  <div className="text-gray-600">通过</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{failedTests}</div>
                  <div className="text-gray-600">失败</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {((passedTests / totalTests) * 100).toFixed(0)}%
                  </div>
                  <div className="text-gray-600">成功率</div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 功能演示区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            功能演示
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button variant="outline" className="h-20 flex flex-col items-center gap-2">
              <FileText className="h-6 w-6" />
              <span>上传Word文档</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col items-center gap-2">
              <MessageSquare className="h-6 w-6" />
              <span>AI对话编辑</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default WordEditingIntegrationTest;
