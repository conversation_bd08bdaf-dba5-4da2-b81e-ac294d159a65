# Word编辑Agent使用指南

## 项目概述

本项目实现了一个基于LangGraph的智能Word文档编辑Agent，支持大文档的智能分章节处理和AI驱动的文档编辑功能。

## 功能特性

### 🚀 核心功能

1. **智能文档分章节**
   - 自动识别Word文档的章节结构
   - 按Token数量智能分割大文档
   - 支持自定义分割参数
   - 确保每个章节不超过模型上下文限制（128K tokens）

2. **AI驱动的文档编辑**
   - 通过自然语言对话编辑Word文档
   - 支持段落添加、文本格式化、内容替换等操作
   - 实时预览编辑结果
   - 智能保存文档更改

3. **前后端分离架构**
   - 左侧：AI聊天界面，支持自然语言交互
   - 右侧：Word文档预览，显示章节结构和内容
   - 实时同步编辑状态

### 🛠️ 技术架构

#### 后端技术栈
- **LangGraph**: 智能Agent框架
- **MCP (Model Context Protocol)**: 工具集成协议
- **FastMCP**: MCP服务器实现
- **tiktoken**: Token计算
- **office-word-mcp-server**: Word文档操作

#### 前端技术栈
- **Next.js**: React框架
- **TypeScript**: 类型安全
- **Tailwind CSS**: 样式框架
- **Radix UI**: 组件库

## 安装和配置

### 1. 后端设置

```bash
cd backend

# 安装依赖
pip install -r requirements.txt

# 启动后端服务
langgraph dev --allow-blocking
```

### 2. 前端设置

```bash
cd frontend

# 安装依赖
pnpm install

# 启动前端服务
pnpm dev
```

### 3. MCP服务配置

后端已配置以下MCP服务：

- `wordDocumentServer`: 基础Word文档操作
- `wordSectionManager`: 文档章节管理
- `enhancedWordProcessor`: 增强Word处理器（主要入口）

## 使用方法

### 基本工作流程

1. **启动服务**
   ```bash
   # 后端
   cd backend && langgraph dev --allow-blocking
   
   # 前端
   cd frontend && pnpm dev
   ```

2. **上传Word文档**
   - 在右侧Word预览区域点击"上传文件"按钮
   - 选择.docx或.doc格式的Word文档
   - 系统自动进行章节分析和分割

3. **选择编辑章节**
   - 在右侧章节列表中选择要编辑的章节
   - 查看章节的Token数量和内容预览
   - 点击"开始编辑"按钮

4. **AI对话编辑**
   - 在左侧聊天界面输入编辑需求
   - 例如："请在第一段后添加一个新段落，内容是..."
   - 例如："请将标题设置为粗体蓝色"
   - 例如："请替换所有的'旧词'为'新词'"

5. **保存文档**
   - 编辑完成后，在聊天界面输入"保存文档"
   - 系统自动保存所有更改

### 高级功能

#### 1. 自定义章节分割

```
请加载文档并设置每个章节最大20000个Token
```

#### 2. 批量格式化

```
请将所有一级标题设置为18号字体，粗体，蓝色
```

#### 3. 内容分析

```
请分析当前章节的主要内容，并提供改进建议
```

## API接口

### 后端MCP工具

#### EnhancedWordProcessor

1. **load_and_split_document**
   ```python
   # 加载并分割文档
   await load_and_split_document(
       file_path="/path/to/document.docx",
       max_tokens_per_section=30000
   )
   ```

2. **select_section_for_editing**
   ```python
   # 选择编辑章节
   await select_section_for_editing(section_id="section_1")
   ```

3. **edit_current_section**
   ```python
   # 编辑当前章节
   await edit_current_section(
       operation="add_paragraph",
       content="新段落内容",
       format_options={"style": "Normal"}
   )
   ```

#### WordSectionManager

1. **get_document_sections**
   ```python
   # 获取所有章节
   await get_document_sections(document_id="doc_123")
   ```

2. **get_section_content**
   ```python
   # 获取章节内容
   await get_section_content(
       document_id="doc_123",
       section_id="section_1"
   )
   ```

## 测试

### 运行后端测试

```bash
cd backend
python test_word_editing_agent.py
```

### 前端集成测试

访问前端应用，使用内置的集成测试组件验证功能。

## 配置参数

### 章节分割配置

```python
{
    "max_tokens_per_section": 30000,  # 每章节最大Token数
    "min_tokens_per_section": 1000,   # 每章节最小Token数
    "overlap_tokens": 500,            # 章节间重叠Token数
    "split_by_headings": True,        # 是否按标题分割
    "heading_levels": [1, 2, 3]       # 识别的标题级别
}
```

### 模型限制

- **上下文窗口**: 128K tokens
- **最大输出**: 96K tokens
- **建议章节大小**: 30K tokens以下

## 故障排除

### 常见问题

1. **文档加载失败**
   - 检查文件路径是否正确
   - 确认文件格式为.docx或.doc
   - 验证文件是否损坏

2. **章节分割异常**
   - 检查文档是否包含标题结构
   - 调整max_tokens_per_section参数
   - 查看后端日志获取详细错误信息

3. **编辑操作失败**
   - 确认已选择要编辑的章节
   - 检查编辑操作的参数格式
   - 验证Word文档是否被其他程序占用

### 日志查看

```bash
# 后端日志
tail -f backend/logs/agent.log

# MCP服务日志
tail -f backend/logs/mcp.log
```

## 开发指南

### 添加新的编辑功能

1. 在`enhanced_word_processor.py`中添加新的工具函数
2. 更新`agent_prompts.txt`中的工具描述
3. 在前端添加相应的UI组件
4. 编写测试用例验证功能

### 扩展章节分割算法

1. 修改`word_section_manager.py`中的分割逻辑
2. 添加新的分割策略（如按页数、按字数等）
3. 更新配置参数和文档

## 贡献指南

1. Fork项目仓库
2. 创建功能分支
3. 提交代码更改
4. 编写测试用例
5. 提交Pull Request

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交Issue到GitHub仓库
- 发送邮件到开发团队
- 参与项目讨论区

---

**注意**: 本项目仍在积极开发中，功能和API可能会有变化。请关注更新日志获取最新信息。
