"""
增强的Word文档处理器
集成word-document-server和章节管理功能
"""

import os
import json
import asyncio
import logging
import subprocess
from typing import List, Dict, Any, Optional
from pathlib import Path
from mcp.server.fastmcp import FastMCP
from pydantic import BaseModel, Field

# 初始化MCP服务器
mcp = FastMCP("EnhancedWordProcessor")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WordEditRequest(BaseModel):
    """Word编辑请求模型"""
    document_id: str = Field(description="文档ID")
    section_id: str = Field(description="章节ID")
    operation: str = Field(description="操作类型: edit, format, insert, delete")
    content: Optional[str] = Field(default=None, description="要编辑的内容")
    position: Optional[int] = Field(default=None, description="插入位置")
    format_options: Optional[Dict[str, Any]] = Field(default=None, description="格式选项")

class WordEditResponse(BaseModel):
    """Word编辑响应模型"""
    success: bool = Field(description="操作是否成功")
    message: str = Field(description="响应消息")
    updated_content: Optional[str] = Field(default=None, description="更新后的内容")
    token_count: Optional[int] = Field(default=None, description="Token数量")

# 全局变量存储当前编辑状态
current_document_id: Optional[str] = None
current_section_id: Optional[str] = None
editing_context: Dict[str, Any] = {}

async def call_word_document_server(tool_name: str, **kwargs) -> Dict[str, Any]:
    """调用word-document-server的工具"""
    try:
        # 这里应该通过MCP客户端调用word-document-server
        # 暂时返回模拟响应
        logger.info(f"调用word-document-server工具: {tool_name}, 参数: {kwargs}")
        
        if tool_name == "get_document_text":
            return {
                "success": True,
                "content": "这是从Word文档中提取的文本内容...",
                "word_count": 1000
            }
        elif tool_name == "create_document":
            return {
                "success": True,
                "message": "文档创建成功",
                "filename": kwargs.get("filename", "new_document.docx")
            }
        elif tool_name == "add_paragraph":
            return {
                "success": True,
                "message": "段落添加成功"
            }
        elif tool_name == "format_text":
            return {
                "success": True,
                "message": "文本格式化成功"
            }
        else:
            return {
                "success": False,
                "error": f"未知的工具: {tool_name}"
            }
            
    except Exception as e:
        logger.error(f"调用word-document-server失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }

async def call_section_manager(tool_name: str, **kwargs) -> Dict[str, Any]:
    """调用章节管理器的工具"""
    try:
        # 这里应该通过MCP客户端调用word_section_manager
        # 暂时返回模拟响应
        logger.info(f"调用章节管理器工具: {tool_name}, 参数: {kwargs}")
        
        if tool_name == "load_word_document":
            return {
                "success": True,
                "document_id": "doc_123",
                "message": "文档加载成功",
                "sections_count": 5
            }
        elif tool_name == "get_section_content":
            return {
                "success": True,
                "section": {
                    "id": kwargs.get("section_id"),
                    "title": "示例章节",
                    "content": "这是章节的内容...",
                    "token_count": 500
                }
            }
        else:
            return {
                "success": False,
                "error": f"未知的工具: {tool_name}"
            }
            
    except Exception as e:
        logger.error(f"调用章节管理器失败: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@mcp.tool()
async def load_and_split_document(file_path: str, max_tokens_per_section: int = 30000) -> str:
    """
    加载Word文档并进行智能分章节
    
    Args:
        file_path: Word文档路径
        max_tokens_per_section: 每个章节最大Token数
    
    Returns:
        JSON格式的文档和章节信息
    """
    try:
        global current_document_id, editing_context
        
        # 首先调用word-document-server获取文档内容
        word_result = await call_word_document_server(
            "get_document_text",
            filename=file_path
        )
        
        if not word_result.get("success"):
            return json.dumps({
                "success": False,
                "error": word_result.get("error", "获取文档内容失败"),
                "message": "无法读取Word文档"
            }, ensure_ascii=False, indent=2)
        
        # 然后调用章节管理器进行分割
        section_result = await call_section_manager(
            "load_word_document",
            file_path=file_path,
            config={"max_tokens_per_section": max_tokens_per_section}
        )
        
        if not section_result.get("success"):
            return json.dumps({
                "success": False,
                "error": section_result.get("error", "章节分割失败"),
                "message": "文档分章节处理失败"
            }, ensure_ascii=False, indent=2)
        
        # 更新全局状态
        current_document_id = section_result.get("document_id")
        editing_context = {
            "file_path": file_path,
            "document_id": current_document_id,
            "sections_count": section_result.get("sections_count", 0),
            "loaded_at": asyncio.get_event_loop().time()
        }
        
        return json.dumps({
            "success": True,
            "document_id": current_document_id,
            "file_path": file_path,
            "sections_count": section_result.get("sections_count", 0),
            "message": f"文档加载成功，已分割为{section_result.get('sections_count', 0)}个章节",
            "context_info": {
                "max_tokens_per_section": max_tokens_per_section,
                "ready_for_editing": True
            }
        }, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"加载和分割文档失败: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "message": "文档处理过程中发生错误"
        }, ensure_ascii=False, indent=2)

@mcp.tool()
async def select_section_for_editing(section_id: str) -> str:
    """
    选择要编辑的章节
    
    Args:
        section_id: 章节ID
    
    Returns:
        JSON格式的章节信息和编辑准备状态
    """
    try:
        global current_section_id, editing_context
        
        if not current_document_id:
            return json.dumps({
                "success": False,
                "error": "No document loaded",
                "message": "请先加载文档"
            }, ensure_ascii=False, indent=2)
        
        # 获取章节内容
        section_result = await call_section_manager(
            "get_section_content",
            document_id=current_document_id,
            section_id=section_id
        )
        
        if not section_result.get("success"):
            return json.dumps({
                "success": False,
                "error": section_result.get("error", "章节不存在"),
                "message": "无法获取章节信息"
            }, ensure_ascii=False, indent=2)
        
        # 更新当前编辑状态
        current_section_id = section_id
        section_info = section_result.get("section", {})
        
        editing_context.update({
            "current_section_id": section_id,
            "section_title": section_info.get("title", ""),
            "section_token_count": section_info.get("token_count", 0),
            "selected_at": asyncio.get_event_loop().time()
        })
        
        return json.dumps({
            "success": True,
            "document_id": current_document_id,
            "section_id": section_id,
            "section_info": section_info,
            "editing_ready": True,
            "context_info": {
                "token_count": section_info.get("token_count", 0),
                "fits_in_context": section_info.get("token_count", 0) <= 120000,
                "can_edit": True
            },
            "message": f"已选择章节: {section_info.get('title', section_id)}"
        }, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"选择章节失败: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "message": "选择章节时发生错误"
        }, ensure_ascii=False, indent=2)

@mcp.tool()
async def edit_current_section(operation: str, content: str = "", format_options: Optional[Dict[str, Any]] = None) -> str:
    """
    编辑当前选中的章节
    
    Args:
        operation: 操作类型 (add_paragraph, format_text, replace_text, insert_text)
        content: 要添加或修改的内容
        format_options: 格式选项 (bold, italic, color等)
    
    Returns:
        JSON格式的编辑结果
    """
    try:
        if not current_document_id or not current_section_id:
            return json.dumps({
                "success": False,
                "error": "No section selected",
                "message": "请先选择要编辑的章节"
            }, ensure_ascii=False, indent=2)
        
        # 根据操作类型调用相应的word-document-server工具
        if operation == "add_paragraph":
            result = await call_word_document_server(
                "add_paragraph",
                filename=editing_context.get("file_path"),
                text=content,
                style=format_options.get("style") if format_options else None
            )
        elif operation == "format_text":
            result = await call_word_document_server(
                "format_text",
                filename=editing_context.get("file_path"),
                paragraph_index=format_options.get("paragraph_index", 0) if format_options else 0,
                start_pos=format_options.get("start_pos", 0) if format_options else 0,
                end_pos=format_options.get("end_pos", len(content)) if format_options else len(content),
                **format_options if format_options else {}
            )
        else:
            return json.dumps({
                "success": False,
                "error": f"Unsupported operation: {operation}",
                "message": f"不支持的操作类型: {operation}"
            }, ensure_ascii=False, indent=2)
        
        if result.get("success"):
            # 更新编辑上下文
            editing_context.update({
                "last_edit_operation": operation,
                "last_edit_content": content,
                "last_edit_time": asyncio.get_event_loop().time()
            })
            
            return json.dumps({
                "success": True,
                "operation": operation,
                "document_id": current_document_id,
                "section_id": current_section_id,
                "message": f"章节编辑成功: {result.get('message', '操作完成')}",
                "edit_info": {
                    "operation_type": operation,
                    "content_length": len(content),
                    "has_formatting": bool(format_options)
                }
            }, ensure_ascii=False, indent=2)
        else:
            return json.dumps({
                "success": False,
                "error": result.get("error", "编辑操作失败"),
                "message": "Word文档编辑失败"
            }, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"编辑章节失败: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "message": "编辑过程中发生错误"
        }, ensure_ascii=False, indent=2)

@mcp.tool()
async def get_editing_status() -> str:
    """
    获取当前编辑状态
    
    Returns:
        JSON格式的编辑状态信息
    """
    try:
        return json.dumps({
            "success": True,
            "editing_context": editing_context,
            "current_document_id": current_document_id,
            "current_section_id": current_section_id,
            "is_ready_for_editing": bool(current_document_id and current_section_id),
            "status": {
                "document_loaded": bool(current_document_id),
                "section_selected": bool(current_section_id),
                "can_edit": bool(current_document_id and current_section_id)
            }
        }, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"获取编辑状态失败: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "message": "获取状态信息失败"
        }, ensure_ascii=False, indent=2)

@mcp.tool()
async def save_document_changes() -> str:
    """
    保存文档更改
    
    Returns:
        JSON格式的保存结果
    """
    try:
        if not current_document_id:
            return json.dumps({
                "success": False,
                "error": "No document to save",
                "message": "没有可保存的文档"
            }, ensure_ascii=False, indent=2)
        
        # 这里应该调用word-document-server的保存功能
        # 暂时返回成功响应
        return json.dumps({
            "success": True,
            "document_id": current_document_id,
            "file_path": editing_context.get("file_path"),
            "message": "文档保存成功",
            "save_info": {
                "saved_at": asyncio.get_event_loop().time(),
                "sections_count": editing_context.get("sections_count", 0)
            }
        }, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"保存文档失败: {e}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "message": "保存文档时发生错误"
        }, ensure_ascii=False, indent=2)

if __name__ == "__main__":
    logger.info("增强Word处理器MCP服务器启动中...")
    mcp.run(transport='stdio')
