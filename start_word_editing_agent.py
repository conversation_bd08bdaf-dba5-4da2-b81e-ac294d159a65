#!/usr/bin/env python3
"""
Word编辑Agent启动脚本
同时启动前端和后端服务
"""

import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path

# 项目路径
PROJECT_ROOT = Path(__file__).parent
BACKEND_DIR = PROJECT_ROOT / "backend"
FRONTEND_DIR = PROJECT_ROOT / "frontend"

# 进程列表
processes = []

def signal_handler(sig, frame):
    """处理Ctrl+C信号，优雅关闭所有进程"""
    print("\n🛑 正在关闭所有服务...")
    
    for process in processes:
        if process.poll() is None:  # 进程仍在运行
            print(f"关闭进程 PID: {process.pid}")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
    
    print("✅ 所有服务已关闭")
    sys.exit(0)

def start_backend():
    """启动后端服务"""
    print("🚀 启动后端API服务...")
    
    # 检查是否在虚拟环境中
    if not os.environ.get('VIRTUAL_ENV'):
        print("⚠️  建议在虚拟环境中运行")
    
    # 启动FastAPI服务器
    try:
        process = subprocess.Popen(
            [sys.executable, "api_routes.py"],
            cwd=BACKEND_DIR,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        processes.append(process)
        
        # 在后台线程中输出日志
        def log_output():
            for line in process.stdout:
                print(f"[后端] {line.strip()}")
        
        threading.Thread(target=log_output, daemon=True).start()
        
        print("✅ 后端API服务启动成功 (http://localhost:8001)")
        return process
        
    except Exception as e:
        print(f"❌ 后端服务启动失败: {e}")
        return None

def start_langgraph():
    """启动LangGraph开发服务器"""
    print("🤖 启动LangGraph Agent服务...")
    
    try:
        process = subprocess.Popen(
            ["langgraph", "dev", "--allow-blocking"],
            cwd=BACKEND_DIR,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        processes.append(process)
        
        # 在后台线程中输出日志
        def log_output():
            for line in process.stdout:
                print(f"[LangGraph] {line.strip()}")
        
        threading.Thread(target=log_output, daemon=True).start()
        
        print("✅ LangGraph服务启动成功")
        return process
        
    except Exception as e:
        print(f"❌ LangGraph服务启动失败: {e}")
        return None

def start_frontend():
    """启动前端服务"""
    print("🌐 启动前端开发服务器...")
    
    # 检查是否安装了pnpm
    try:
        subprocess.run(["pnpm", "--version"], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 未找到pnpm，请先安装: npm install -g pnpm")
        return None
    
    try:
        process = subprocess.Popen(
            ["pnpm", "dev"],
            cwd=FRONTEND_DIR,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        processes.append(process)
        
        # 在后台线程中输出日志
        def log_output():
            for line in process.stdout:
                print(f"[前端] {line.strip()}")
        
        threading.Thread(target=log_output, daemon=True).start()
        
        print("✅ 前端开发服务器启动成功 (http://localhost:3000)")
        return process
        
    except Exception as e:
        print(f"❌ 前端服务启动失败: {e}")
        return None

def check_dependencies():
    """检查依赖是否安装"""
    print("🔍 检查依赖...")
    
    # 检查后端依赖
    try:
        import fastapi
        import uvicorn
        print("✅ 后端依赖检查通过")
    except ImportError as e:
        print(f"❌ 后端依赖缺失: {e}")
        print("请运行: pip install -r backend/requirements.txt")
        return False
    
    # 检查前端依赖
    if not (FRONTEND_DIR / "node_modules").exists():
        print("❌ 前端依赖未安装")
        print("请运行: cd frontend && pnpm install")
        return False
    
    print("✅ 前端依赖检查通过")
    return True

def main():
    """主函数"""
    print("🎯 Word编辑Agent启动器")
    print("=" * 50)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    print("\n🚀 启动所有服务...")
    
    # 启动后端API服务
    backend_process = start_backend()
    if not backend_process:
        print("❌ 后端服务启动失败，退出")
        sys.exit(1)
    
    # 等待后端服务启动
    time.sleep(3)
    
    # 启动LangGraph服务
    langgraph_process = start_langgraph()
    if not langgraph_process:
        print("❌ LangGraph服务启动失败，退出")
        sys.exit(1)
    
    # 等待LangGraph服务启动
    time.sleep(5)
    
    # 启动前端服务
    # frontend_process = start_frontend()
    # if not frontend_process:
    #     print("❌ 前端服务启动失败，退出")
    #     sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 所有服务启动成功！")
    print("📱 前端地址: http://localhost:3000")
    print("🔧 后端API: http://localhost:8001")
    print("🤖 LangGraph: http://localhost:8123")
    print("=" * 50)
    print("按 Ctrl+C 停止所有服务")
    
    # 等待所有进程
    try:
        while True:
            # 检查进程是否还在运行
            running_processes = [p for p in processes if p.poll() is None]
            if not running_processes:
                print("⚠️  所有进程已退出")
                break
            time.sleep(1)
    except KeyboardInterrupt:
        pass
    
    signal_handler(None, None)

if __name__ == "__main__":
    main()
