"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/favicon.ico/route";
exports.ids = ["app/favicon.ico/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=G%3A%5CStudy%5CPython%5Cbackup%5CAgent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CStudy%5CPython%5Cbackup%5CAgent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=G%3A%5CStudy%5CPython%5Cbackup%5CAgent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CStudy%5CPython%5Cbackup%5CAgent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_metadata_route_loader_filePath_G_3A_5CStudy_5CPython_5Cbackup_5CAgent_5Cfrontend_5Csrc_5Capp_5Cfavicon_ico_isDynamicRouteExtension_0_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-metadata-route-loader?filePath=G%3A%5CStudy%5CPython%5Cbackup%5CAgent%5Cfrontend%5Csrc%5Capp%5Cfavicon.ico&isDynamicRouteExtension=0!?__next_metadata_route__ */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?filePath=G%3A%5CStudy%5CPython%5Cbackup%5CAgent%5Cfrontend%5Csrc%5Capp%5Cfavicon.ico&isDynamicRouteExtension=0!?__next_metadata_route__\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/favicon.ico/route\",\n        pathname: \"/favicon.ico\",\n        filename: \"favicon\",\n        bundlePath: \"app/favicon.ico/route\"\n    },\n    resolvedPagePath: \"next-metadata-route-loader?filePath=G%3A%5CStudy%5CPython%5Cbackup%5CAgent%5Cfrontend%5Csrc%5Capp%5Cfavicon.ico&isDynamicRouteExtension=0!?__next_metadata_route__\",\n    nextConfigOutput,\n    userland: next_metadata_route_loader_filePath_G_3A_5CStudy_5CPython_5Cbackup_5CAgent_5Cfrontend_5Csrc_5Capp_5Cfavicon_ico_isDynamicRouteExtension_0_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=G%3A%5CStudy%5CPython%5Cbackup%5CAgent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CStudy%5CPython%5Cbackup%5CAgent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?filePath=G%3A%5CStudy%5CPython%5Cbackup%5CAgent%5Cfrontend%5Csrc%5Capp%5Cfavicon.ico&isDynamicRouteExtension=0!?__next_metadata_route__":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?filePath=G%3A%5CStudy%5CPython%5Cbackup%5CAgent%5Cfrontend%5Csrc%5Capp%5Cfavicon.ico&isDynamicRouteExtension=0!?__next_metadata_route__ ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/api/server.js\");\n/* static asset route */\n\n\nconst contentType = \"image/x-icon\"\nconst buffer = Buffer.from(\"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\", 'base64'\n  )\n\nif (false) {}\n\nfunction GET() {\n  return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(buffer, {\n    headers: {\n      'Content-Type': contentType,\n      'Cache-Control': \"public, max-age=0, must-revalidate\",\n    },\n  })\n}\n\nconst dynamic = 'force-static'\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?filePath=G%3A%5CStudy%5CPython%5Cbackup%5CAgent%5Cfrontend%5Csrc%5Capp%5Cfavicon.ico&isDynamicRouteExtension=0!?__next_metadata_route__\n");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=G%3A%5CStudy%5CPython%5Cbackup%5CAgent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CStudy%5CPython%5Cbackup%5CAgent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();