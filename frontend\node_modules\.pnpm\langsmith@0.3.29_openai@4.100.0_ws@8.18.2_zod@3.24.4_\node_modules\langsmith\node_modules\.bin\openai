#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/openai@4.100.0_ws@8.18.2_zod@3.24.4/node_modules/openai/bin/node_modules:/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/openai@4.100.0_ws@8.18.2_zod@3.24.4/node_modules/openai/node_modules:/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/openai@4.100.0_ws@8.18.2_zod@3.24.4/node_modules:/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/openai@4.100.0_ws@8.18.2_zod@3.24.4/node_modules/openai/bin/node_modules:/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/openai@4.100.0_ws@8.18.2_zod@3.24.4/node_modules/openai/node_modules:/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/openai@4.100.0_ws@8.18.2_zod@3.24.4/node_modules:/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../openai/bin/cli" "$@"
else
  exec node  "$basedir/../../../openai/bin/cli" "$@"
fi
