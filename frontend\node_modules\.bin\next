#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/bin/node_modules:/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/node_modules:/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/node_modules:/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules:/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/bin/node_modules:/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/dist/node_modules:/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules/next/node_modules:/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/next@15.3.2_@playwright+tes_11ef3c70f816038b1e7819c982d20629/node_modules:/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../next/dist/bin/next" "$@"
else
  exec node  "$basedir/../next/dist/bin/next" "$@"
fi
