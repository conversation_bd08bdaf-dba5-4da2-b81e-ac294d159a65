"""
简化的测试API服务器
用于快速测试前端功能，不依赖完整的LangGraph Agent
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(title="Word编辑Agent测试API", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # 前端地址
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 上传目录
upload_dir = Path("uploads")
upload_dir.mkdir(exist_ok=True)

class LoadDocumentRequest(BaseModel):
    file_path: str
    max_tokens_per_section: int = 30000

@app.post("/api/word/upload")
async def upload_word_document(file: UploadFile = File(...)):
    """上传Word文档"""
    try:
        logger.info(f"接收到文件上传请求: {file.filename}")
        
        # 验证文件类型
        if not file.filename or not file.filename.lower().endswith(('.doc', '.docx')):
            raise HTTPException(status_code=400, detail="请上传Word文档文件 (.doc 或 .docx)")
        
        # 保存文件
        file_path = upload_dir / file.filename
        
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        logger.info(f"文件上传成功: {file_path}, 大小: {len(content)} bytes")
        
        return {
            "success": True,
            "message": "文件上传成功",
            "filePath": str(file_path),
            "fileName": file.filename,
            "fileSize": len(content)
        }
        
    except Exception as e:
        logger.error(f"文件上传失败: {e}")
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

@app.post("/api/word/load-and-split")
async def load_and_split_document(request: LoadDocumentRequest):
    """加载并分割Word文档（模拟实现）"""
    try:
        logger.info(f"接收到文档加载请求: {request.file_path}")
        
        # 检查文件是否存在
        file_path = Path(request.file_path)
        if not file_path.exists():
            raise HTTPException(status_code=404, detail="文件不存在")
        
        # 模拟文档分析过程
        import time
        time.sleep(2)  # 模拟处理时间
        
        # 返回模拟的章节数据
        mock_sections = [
            {
                "id": "section_1",
                "title": "第一章 引言",
                "content": "这是第一章的内容，介绍了文档的背景和目的。本章将详细阐述研究的意义和价值，为后续章节奠定基础。",
                "level": 1,
                "word_count": 1200,
                "token_count": 800
            },
            {
                "id": "section_1_1",
                "title": "1.1 研究背景",
                "content": "在当前的技术发展背景下，人工智能技术正在快速发展。本研究旨在探索AI在文档处理领域的应用。",
                "level": 2,
                "word_count": 800,
                "token_count": 600
            },
            {
                "id": "section_1_2",
                "title": "1.2 研究目标",
                "content": "本研究的主要目标包括：1. 开发智能文档处理系统；2. 提高文档处理效率；3. 改善用户体验。",
                "level": 2,
                "word_count": 600,
                "token_count": 450
            },
            {
                "id": "section_2",
                "title": "第二章 方法论",
                "content": "本章介绍了研究采用的方法和技术路线。我们采用了先进的自然语言处理技术和机器学习算法。",
                "level": 1,
                "word_count": 1500,
                "token_count": 1100
            },
            {
                "id": "section_2_1",
                "title": "2.1 技术架构",
                "content": "系统采用微服务架构，包含前端界面、后端服务、数据存储等组件。每个组件都经过精心设计。",
                "level": 2,
                "word_count": 900,
                "token_count": 700
            },
            {
                "id": "section_3",
                "title": "第三章 实验结果",
                "content": "本章展示了实验的详细结果和分析。通过大量的测试验证了系统的有效性和可靠性。",
                "level": 1,
                "word_count": 2000,
                "token_count": 1500
            }
        ]
        
        response = {
            "success": True,
            "document_id": f"doc_{hash(str(file_path))}",
            "message": f"文档加载和分割成功，共分析出{len(mock_sections)}个章节",
            "document": {
                "name": file_path.name,
                "sections": mock_sections,
                "total_words": sum(s["word_count"] for s in mock_sections),
                "total_tokens": sum(s["token_count"] for s in mock_sections)
            }
        }
        
        logger.info(f"文档分析完成: {len(mock_sections)}个章节")
        return response
        
    except Exception as e:
        logger.error(f"文档加载失败: {e}")
        raise HTTPException(status_code=500, detail=f"文档加载失败: {str(e)}")

@app.post("/api/word/select-section")
async def select_section_for_editing(request: dict):
    """选择章节进行编辑（模拟实现）"""
    try:
        section_id = request.get("section_id")
        document_id = request.get("document_id")
        
        logger.info(f"选择章节进行编辑: {section_id}")
        
        return {
            "success": True,
            "message": f"已选择章节 {section_id} 进行编辑",
            "document_id": document_id,
            "section_id": section_id,
            "ready_for_editing": True
        }
        
    except Exception as e:
        logger.error(f"选择章节失败: {e}")
        raise HTTPException(status_code=500, detail=f"选择章节失败: {str(e)}")

@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "message": "Word编辑Agent测试API运行正常",
        "upload_dir": str(upload_dir.absolute()),
        "uploaded_files": [f.name for f in upload_dir.glob("*") if f.is_file()]
    }

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "Word编辑Agent测试API",
        "version": "1.0.0",
        "endpoints": [
            "/api/word/upload",
            "/api/word/load-and-split", 
            "/api/word/select-section",
            "/api/health"
        ]
    }

if __name__ == "__main__":
    print("🚀 启动Word编辑Agent测试API服务器...")
    print("📁 上传目录:", upload_dir.absolute())
    print("🌐 API地址: http://localhost:8001")
    print("📖 API文档: http://localhost:8001/docs")
    
    uvicorn.run(
        "test_api_server:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )
