"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { 
  FileText, 
  ChevronRight, 
  ChevronDown, 
  RefreshCw,
  Upload,
  Download,
  Eye,
  EyeOff
} from "lucide-react";
import { cn } from "@/lib/utils";
import FileUpload from "./file-upload";

interface WordSection {
  id: string;
  title: string;
  content: string;
  level: number;
  wordCount: number;
  selected?: boolean;
}

interface WordDocument {
  id: string;
  name: string;
  path: string;
  sections: WordSection[];
  totalWords: number;
  lastModified: Date;
}

interface WordPreviewProps {
  className?: string;
  onSectionSelect?: (section: WordSection) => void;
  onDocumentLoad?: (document: WordDocument) => void;
  onStartEditing?: (sectionId: string) => void;
}

export function WordPreview({ className, onSectionSelect, onDocumentLoad, onStartEditing }: WordPreviewProps) {
  const [document, setDocument] = useState<WordDocument | null>(null);
  const [selectedSection, setSelectedSection] = useState<string | null>(null);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(false);
  const [previewMode, setPreviewMode] = useState<'sections' | 'content'>('sections');
  const [showUpload, setShowUpload] = useState(false);

  // 加载Word文档并进行章节分割
  const loadDocument = async (filePath?: string) => {
    setIsLoading(true);
    try {
      // 调用后端API来加载和解析Word文档
      // 这里应该通过WebSocket或HTTP API调用后端的enhanced_word_processor
      const mockDocument: WordDocument = {
        id: "doc-1",
        name: "示例文档.docx",
        path: filePath || "/path/to/document.docx",
        sections: [
          {
            id: "section-1",
            title: "第一章 引言",
            content: "这是第一章的内容，介绍了文档的背景和目的...",
            level: 1,
            wordCount: 1200
          },
          {
            id: "section-1-1",
            title: "1.1 研究背景",
            content: "研究背景的详细描述...",
            level: 2,
            wordCount: 800
          },
          {
            id: "section-1-2",
            title: "1.2 研究目标",
            content: "研究目标的详细说明...",
            level: 2,
            wordCount: 600
          },
          {
            id: "section-2",
            title: "第二章 方法论",
            content: "这是第二章的内容，详细描述了研究方法...",
            level: 1,
            wordCount: 1500
          },
          {
            id: "section-2-1",
            title: "2.1 数据收集",
            content: "数据收集方法的详细说明...",
            level: 2,
            wordCount: 900
          }
        ],
        totalWords: 5000,
        lastModified: new Date()
      };

      setDocument(mockDocument);
      onDocumentLoad?.(mockDocument);
    } catch (error) {
      console.error("Failed to load document:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSectionClick = (section: WordSection) => {
    setSelectedSection(section.id);
    onSectionSelect?.(section);
  };

  const toggleSectionExpansion = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  const handleFileSelect = (file: File) => {
    console.log("Selected file:", file.name);
    // 这里可以进行文件预处理
  };

  const handleFileLoad = async (filePath: string) => {
    console.log("Loading file:", filePath);
    // 调用loadDocument来处理上传的文件
    await loadDocument(filePath);
    setShowUpload(false);
  };

  const renderSection = (section: WordSection) => {
    const isExpanded = expandedSections.has(section.id);
    const isSelected = selectedSection === section.id;
    const hasSubsections = document?.sections.some(s => 
      s.level > section.level && s.id.startsWith(section.id.split('-')[0])
    );

    return (
      <div key={section.id} className="mb-2">
        <div
          className={cn(
            "flex items-center gap-2 p-2 rounded-md cursor-pointer hover:bg-gray-50 transition-colors",
            isSelected && "bg-blue-50 border border-blue-200",
            `ml-${(section.level - 1) * 4}`
          )}
          onClick={() => handleSectionClick(section)}
        >
          {hasSubsections && (
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0"
              onClick={(e) => {
                e.stopPropagation();
                toggleSectionExpansion(section.id);
              }}
            >
              {isExpanded ? (
                <ChevronDown className="h-3 w-3" />
              ) : (
                <ChevronRight className="h-3 w-3" />
              )}
            </Button>
          )}
          <FileText className="h-4 w-4 text-gray-500" />
          <span className="flex-1 text-sm font-medium">{section.title}</span>
          <Badge variant="secondary" className="text-xs">
            {section.wordCount}字
          </Badge>
        </div>
        
        {previewMode === 'content' && isSelected && (
          <div className="mt-2 p-3 bg-gray-50 rounded-md ml-6">
            <p className="text-sm text-gray-700 line-clamp-3">
              {section.content}
            </p>
          </div>
        )}
      </div>
    );
  };

  useEffect(() => {
    // 组件挂载时加载默认文档
    loadDocument();
  }, []);

  return (
    <div className={cn("flex flex-col h-full bg-white", className)}>
      {/* 头部工具栏 */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <FileText className="h-5 w-5 text-blue-600" />
          <span className="font-semibold text-gray-900">Word文档预览</span>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowUpload(!showUpload)}
          >
            <Upload className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setPreviewMode(previewMode === 'sections' ? 'content' : 'sections')}
          >
            {previewMode === 'sections' ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => loadDocument()}
            disabled={isLoading}
          >
            <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
          </Button>
        </div>
      </div>

      {/* 文档信息 */}
      {document && (
        <div className="p-4 bg-gray-50 border-b">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-medium text-gray-900">{document.name}</h3>
            <Badge variant="outline">{document.totalWords}字</Badge>
          </div>
          <p className="text-sm text-gray-500">
            最后修改: {document.lastModified.toLocaleString()}
          </p>
        </div>
      )}

      {/* 文件上传区域 */}
      {showUpload && (
        <div className="p-4 border-b bg-gray-50">
          <FileUpload
            onFileSelect={handleFileSelect}
            onFileLoad={handleFileLoad}
            maxSize={50}
          />
        </div>
      )}

      {/* 章节列表 */}
      <ScrollArea className="flex-1 p-4">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
            <span className="ml-2 text-gray-500">加载中...</span>
          </div>
        ) : document ? (
          <div className="space-y-1">
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">文档章节</h4>
              <Separator />
            </div>
            {document.sections.map(renderSection)}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-32 text-gray-500">
            <FileText className="h-8 w-8 mb-2" />
            <p className="text-sm">请上传Word文档</p>
            <Button
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={() => setShowUpload(true)}
            >
              <Upload className="h-4 w-4 mr-2" />
              选择文件
            </Button>
          </div>
        )}
      </ScrollArea>

      {/* 底部操作栏 */}
      {document && selectedSection && (
        <div className="p-4 border-t bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <span className="text-sm text-gray-600">
                已选择: {document.sections.find(s => s.id === selectedSection)?.title}
              </span>
              <div className="text-xs text-gray-500 mt-1">
                Token数: {document.sections.find(s => s.id === selectedSection)?.tokenCount || 0}
              </div>
            </div>
            <Button
              size="sm"
              variant="default"
              onClick={() => onStartEditing?.(selectedSection)}
            >
              开始编辑
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

export default WordPreview;
