"""
API路由处理器
处理前端的Word文档上传和处理请求
"""

import os
import json
import asyncio
import logging
from pathlib import Path
from typing import Dict, Any
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

from graph import make_graph

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(title="Word编辑Agent API", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # 前端地址
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量存储Agent实例
agent = None
upload_dir = Path("uploads")
upload_dir.mkdir(exist_ok=True)

class LoadDocumentRequest(BaseModel):
    file_path: str
    max_tokens_per_section: int = 30000

class SelectSectionRequest(BaseModel):
    document_id: str
    section_id: str

class EditSectionRequest(BaseModel):
    operation: str
    content: str = ""
    format_options: Dict[str, Any] = {}

@app.on_event("startup")
async def startup_event():
    """启动时初始化Agent"""
    global agent
    try:
        logger.info("初始化LangGraph Agent...")
        agent = await make_graph()
        logger.info("Agent初始化成功")
    except Exception as e:
        logger.error(f"Agent初始化失败: {e}")
        raise

@app.post("/api/word/upload")
async def upload_word_document(file: UploadFile = File(...)):
    """上传Word文档"""
    try:
        # 验证文件类型
        if not file.filename or not file.filename.lower().endswith(('.doc', '.docx')):
            raise HTTPException(status_code=400, detail="请上传Word文档文件 (.doc 或 .docx)")
        
        # 保存文件
        file_path = upload_dir / file.filename
        
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        logger.info(f"文件上传成功: {file_path}")
        
        return {
            "success": True,
            "message": "文件上传成功",
            "filePath": str(file_path),
            "fileName": file.filename,
            "fileSize": len(content)
        }
        
    except Exception as e:
        logger.error(f"文件上传失败: {e}")
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

@app.post("/api/word/load-and-split")
async def load_and_split_document(request: LoadDocumentRequest):
    """加载并分割Word文档"""
    try:
        if not agent:
            raise HTTPException(status_code=500, detail="Agent未初始化")
        
        # 构造消息调用Agent
        messages = [{
            "role": "user",
            "content": f"请加载Word文档并进行章节分割。文档路径：{request.file_path}，每个章节最大{request.max_tokens_per_section}个Token。"
        }]
        
        config = {"configurable": {"thread_id": f"load_doc_{hash(request.file_path)}"}}
        
        # 调用Agent
        response = await agent.ainvoke({"messages": messages}, config=config)
        
        # 解析Agent响应
        ai_messages = [msg for msg in response["messages"] if msg.type == "ai"]
        if not ai_messages:
            raise HTTPException(status_code=500, detail="Agent未返回响应")
        
        last_message = ai_messages[-1].content
        
        # 这里需要解析Agent的响应，提取文档信息
        # 暂时返回模拟数据，实际应该解析Agent的工具调用结果
        mock_response = {
            "success": True,
            "document_id": f"doc_{hash(request.file_path)}",
            "message": "文档加载和分割成功",
            "document": {
                "name": os.path.basename(request.file_path),
                "sections": [
                    {
                        "id": "section_1",
                        "title": "第一章",
                        "content": "第一章内容...",
                        "level": 1,
                        "word_count": 1000,
                        "token_count": 800
                    },
                    {
                        "id": "section_2", 
                        "title": "第二章",
                        "content": "第二章内容...",
                        "level": 1,
                        "word_count": 1200,
                        "token_count": 950
                    }
                ],
                "total_words": 2200,
                "total_tokens": 1750
            }
        }
        
        logger.info(f"文档加载成功: {request.file_path}")
        return mock_response
        
    except Exception as e:
        logger.error(f"文档加载失败: {e}")
        raise HTTPException(status_code=500, detail=f"文档加载失败: {str(e)}")

@app.post("/api/word/select-section")
async def select_section_for_editing(request: SelectSectionRequest):
    """选择章节进行编辑"""
    try:
        if not agent:
            raise HTTPException(status_code=500, detail="Agent未初始化")
        
        messages = [{
            "role": "user",
            "content": f"请选择章节 {request.section_id} 进行编辑。文档ID：{request.document_id}"
        }]
        
        config = {"configurable": {"thread_id": f"edit_{request.document_id}"}}
        
        response = await agent.ainvoke({"messages": messages}, config=config)
        
        return {
            "success": True,
            "message": f"已选择章节 {request.section_id} 进行编辑",
            "document_id": request.document_id,
            "section_id": request.section_id
        }
        
    except Exception as e:
        logger.error(f"选择章节失败: {e}")
        raise HTTPException(status_code=500, detail=f"选择章节失败: {str(e)}")

@app.post("/api/word/edit-section")
async def edit_current_section(request: EditSectionRequest):
    """编辑当前章节"""
    try:
        if not agent:
            raise HTTPException(status_code=500, detail="Agent未初始化")
        
        # 构造编辑请求消息
        if request.operation == "add_paragraph":
            content = f"请在当前章节添加段落：{request.content}"
        elif request.operation == "format_text":
            content = f"请格式化文本：{request.content}，格式选项：{request.format_options}"
        else:
            content = f"请执行操作 {request.operation}：{request.content}"
        
        messages = [{
            "role": "user",
            "content": content
        }]
        
        config = {"configurable": {"thread_id": "current_editing_session"}}
        
        response = await agent.ainvoke({"messages": messages}, config=config)
        
        return {
            "success": True,
            "message": f"章节编辑操作 {request.operation} 执行成功",
            "operation": request.operation
        }
        
    except Exception as e:
        logger.error(f"章节编辑失败: {e}")
        raise HTTPException(status_code=500, detail=f"章节编辑失败: {str(e)}")

@app.post("/api/word/save-document")
async def save_document():
    """保存文档更改"""
    try:
        if not agent:
            raise HTTPException(status_code=500, detail="Agent未初始化")
        
        messages = [{
            "role": "user",
            "content": "请保存当前文档的所有更改"
        }]
        
        config = {"configurable": {"thread_id": "current_editing_session"}}
        
        response = await agent.ainvoke({"messages": messages}, config=config)
        
        return {
            "success": True,
            "message": "文档保存成功"
        }
        
    except Exception as e:
        logger.error(f"文档保存失败: {e}")
        raise HTTPException(status_code=500, detail=f"文档保存失败: {str(e)}")

@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "agent_ready": agent is not None,
        "message": "Word编辑Agent API运行正常"
    }

if __name__ == "__main__":
    # 启动API服务器
    uvicorn.run(
        "api_routes:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )
