#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/json5@1.0.2/node_modules/json5/lib/node_modules:/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/json5@1.0.2/node_modules/json5/node_modules:/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/json5@1.0.2/node_modules:/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/json5@1.0.2/node_modules/json5/lib/node_modules:/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/json5@1.0.2/node_modules/json5/node_modules:/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/json5@1.0.2/node_modules:/mnt/g/Study/Python/backup/Agent/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../json5/lib/cli.js" "$@"
else
  exec node  "$basedir/../json5/lib/cli.js" "$@"
fi
